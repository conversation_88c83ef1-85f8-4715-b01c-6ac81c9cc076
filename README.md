# Qwen3 Coder 30B with <PERSON><PERSON><PERSON> in Docker

This repository contains Docker configuration files to deploy the Qwen3 Coder 30B model using Ollama.

## Prerequisites

- Docker and Docker Compose installed
- At least 32GB of RAM (recommended for 30B model)
- NVIDIA GPU with Docker GPU support (optional but recommended for better performance)

## Quick Start

### Method 1: Using Docker Compose (Recommended)

1. Start the Ollama service:
```bash
docker-compose up -d
```

2. Pull the Qwen3 Coder model:
```bash
docker exec ollama-qwen3-coder ollama pull qwen3-coder:30b
```

3. Test the model:
```bash
docker exec -it ollama-qwen3-coder ollama run qwen3-coder:30b
```

### Method 2: Using Docker directly

1. Run Ollama container:
```bash
docker run -d \
  --name ollama-qwen3-coder \
  -p 11434:11434 \
  -v ollama_data:/root/.ollama \
  -e OLLAMA_HOST=0.0.0.0 \
  ollama/ollama:latest
```

2. Pull the model:
```bash
docker exec ollama-qwen3-coder ollama pull qwen3-coder:30b
```

### Method 3: Using custom Dockerfile

1. Build the custom image:
```bash
docker build -t ollama-qwen3-custom .
```

2. Run the container:
```bash
docker run -d \
  --name ollama-qwen3-coder \
  -p 11434:11434 \
  -v ollama_data:/root/.ollama \
  ollama-qwen3-custom
```

## GPU Support

If you have NVIDIA GPU support, uncomment the GPU configuration in `docker-compose.yml`:

```yaml
deploy:
  resources:
    reservations:
      devices:
        - driver: nvidia
          count: all
          capabilities: [gpu]
```

## API Usage

Once the container is running, you can interact with the model via HTTP API:

```bash
# Generate code completion
curl -X POST http://localhost:11434/api/generate \
  -H "Content-Type: application/json" \
  -d '{
    "model": "qwen3-coder:30b",
    "prompt": "Write a Python function to calculate fibonacci numbers:",
    "stream": false
  }'
```

## Web Interface

You can also use Ollama with a web interface like Open WebUI:

```bash
docker run -d \
  --name open-webui \
  -p 3000:8080 \
  -e OLLAMA_BASE_URL=http://ollama-qwen3-coder:11434 \
  --link ollama-qwen3-coder \
  ghcr.io/open-webui/open-webui:main
```

## Stopping the Service

```bash
docker-compose down
```

## Troubleshooting

- **Out of memory**: The 30B model requires significant RAM. Consider using a smaller model like `qwen3-coder:7b` if you have limited resources.
- **Slow performance**: Enable GPU support for better performance.
- **Connection issues**: Make sure port 11434 is not blocked by firewall.

## Model Information

- **Model**: Qwen3 Coder 30B
- **Size**: ~30GB
- **Use case**: Code generation, completion, and analysis
- **Languages**: Supports multiple programming languages including Python, JavaScript, Java, C++, etc.
