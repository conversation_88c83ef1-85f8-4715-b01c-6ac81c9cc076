#!/usr/bin/env python3
"""
Ollama API测试脚本
用于测试部署的Qwen3 Coder模型
"""

import requests
import json
import time

def test_ollama_api(model_name="qwen3-coder:30b", host="localhost", port=11434):
    """测试Ollama API"""
    base_url = f"http://{host}:{port}"
    
    print(f"测试Ollama API: {base_url}")
    print(f"模型: {model_name}")
    print("-" * 50)
    
    # 1. 检查服务状态
    try:
        response = requests.get(f"{base_url}/api/tags", timeout=10)
        if response.status_code == 200:
            models = response.json()
            print("✅ Ollama服务正常运行")
            print("可用模型:")
            for model in models.get('models', []):
                print(f"  - {model['name']} ({model['size']})")
        else:
            print("❌ Ollama服务异常")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 无法连接到Ollama服务: {e}")
        return False
    
    print()
    
    # 2. 测试代码生成
    test_prompts = [
        "Write a Python function to calculate fibonacci numbers",
        "Create a simple REST API using Flask",
        "Write a JavaScript function to sort an array",
        "Implement a binary search algorithm in Python"
    ]
    
    for i, prompt in enumerate(test_prompts, 1):
        print(f"测试 {i}: {prompt}")
        
        payload = {
            "model": model_name,
            "prompt": prompt,
            "stream": False,
            "options": {
                "temperature": 0.7,
                "max_tokens": 500
            }
        }
        
        try:
            start_time = time.time()
            response = requests.post(
                f"{base_url}/api/generate",
                json=payload,
                timeout=60
            )
            end_time = time.time()
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 响应时间: {end_time - start_time:.2f}秒")
                print("生成的代码:")
                print("-" * 30)
                print(result.get('response', ''))
                print("-" * 30)
            else:
                print(f"❌ API调用失败: {response.status_code}")
                print(response.text)
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求失败: {e}")
        
        print()
        
        # 避免请求过于频繁
        if i < len(test_prompts):
            time.sleep(2)
    
    return True

def test_streaming_api(model_name="qwen3-coder:30b", host="localhost", port=11434):
    """测试流式API"""
    base_url = f"http://{host}:{port}"
    
    print("测试流式API...")
    
    payload = {
        "model": model_name,
        "prompt": "Write a simple Python class for a calculator",
        "stream": True
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/generate",
            json=payload,
            stream=True,
            timeout=60
        )
        
        if response.status_code == 200:
            print("✅ 流式响应:")
            print("-" * 30)
            
            for line in response.iter_lines():
                if line:
                    try:
                        data = json.loads(line.decode('utf-8'))
                        if 'response' in data:
                            print(data['response'], end='', flush=True)
                        if data.get('done', False):
                            break
                    except json.JSONDecodeError:
                        continue
            
            print()
            print("-" * 30)
        else:
            print(f"❌ 流式API调用失败: {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 流式请求失败: {e}")

if __name__ == "__main__":
    import sys
    
    # 从命令行参数获取模型名称
    model_name = sys.argv[1] if len(sys.argv) > 1 else "qwen3-coder:30b"
    
    print("=== Ollama API 测试 ===")
    print()
    
    # 测试基本API
    if test_ollama_api(model_name):
        print()
        # 测试流式API
        test_streaming_api(model_name)
    
    print()
    print("测试完成!")
