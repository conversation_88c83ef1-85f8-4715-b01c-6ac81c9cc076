#!/bin/bash

# Qwen3 Coder 30B Docker部署脚本
# 处理网络问题和重试机制

set -e

echo "=== Qwen3 Coder 30B Docker部署脚本 ==="

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "错误: Docker未运行，请先启动Docker"
    exit 1
fi

# 停止并删除现有容器（如果存在）
echo "清理现有容器..."
docker stop ollama-qwen3-coder 2>/dev/null || true
docker rm ollama-qwen3-coder 2>/dev/null || true

# 拉取Ollama镜像（使用国内镜像源）
echo "拉取Ollama镜像..."
if ! docker pull docker.m.daocloud.io/ollama/ollama:latest; then
    echo "尝试使用其他镜像源..."
    docker pull registry.cn-hangzhou.aliyuncs.com/google_containers/ollama:latest || \
    docker pull ollama/ollama:latest
fi

# 给镜像打标签
docker tag docker.m.daocloud.io/ollama/ollama:latest ollama/ollama:latest 2>/dev/null || true

# 启动Ollama容器
echo "启动Ollama容器..."
docker run -d \
    --name ollama-qwen3-coder \
    -p 11434:11434 \
    -v ollama_data:/root/.ollama \
    -e OLLAMA_HOST=0.0.0.0 \
    --restart unless-stopped \
    ollama/ollama:latest

# 等待容器启动
echo "等待Ollama服务启动..."
sleep 10

# 检查容器状态
if ! docker ps | grep -q ollama-qwen3-coder; then
    echo "错误: 容器启动失败"
    docker logs ollama-qwen3-coder
    exit 1
fi

echo "✅ Ollama容器启动成功!"

# 模型下载函数（带重试机制）
download_model() {
    local model_name=$1
    local max_retries=3
    local retry_count=0
    
    echo "开始下载模型: $model_name"
    
    while [ $retry_count -lt $max_retries ]; do
        echo "尝试 $((retry_count + 1))/$max_retries 下载 $model_name..."
        
        if timeout 3600 docker exec ollama-qwen3-coder ollama pull $model_name; then
            echo "✅ 模型 $model_name 下载成功!"
            return 0
        else
            echo "❌ 下载失败，等待30秒后重试..."
            sleep 30
            retry_count=$((retry_count + 1))
        fi
    done
    
    echo "❌ 模型 $model_name 下载失败，已达到最大重试次数"
    return 1
}

# 尝试下载Qwen3 Coder 30B模型
echo "=== 开始下载模型 ==="
if download_model "qwen3-coder:30b"; then
    MODEL_NAME="qwen3-coder:30b"
elif download_model "codellama:7b"; then
    MODEL_NAME="codellama:7b"
    echo "⚠️  由于网络问题，使用CodeLlama 7B作为替代"
elif download_model "llama3.2:3b"; then
    MODEL_NAME="llama3.2:3b"
    echo "⚠️  由于网络问题，使用Llama3.2 3B作为替代"
else
    echo "❌ 所有模型下载都失败了"
    echo "请检查网络连接或稍后重试"
    exit 1
fi

# 测试模型
echo "=== 测试模型 ==="
echo "测试模型: $MODEL_NAME"
docker exec -it ollama-qwen3-coder ollama run $MODEL_NAME "Write a simple Python hello world function" || true

echo ""
echo "=== 部署完成 ==="
echo "✅ Ollama服务已启动，端口: 11434"
echo "✅ 模型: $MODEL_NAME"
echo ""
echo "使用方法:"
echo "1. 交互式使用:"
echo "   docker exec -it ollama-qwen3-coder ollama run $MODEL_NAME"
echo ""
echo "2. API调用:"
echo "   curl -X POST http://localhost:11434/api/generate \\"
echo "     -H \"Content-Type: application/json\" \\"
echo "     -d '{\"model\": \"$MODEL_NAME\", \"prompt\": \"Write a Python function\", \"stream\": false}'"
echo ""
echo "3. 查看容器状态:"
echo "   docker ps"
echo "   docker logs ollama-qwen3-coder"
echo ""
echo "4. 停止服务:"
echo "   docker stop ollama-qwen3-coder"
