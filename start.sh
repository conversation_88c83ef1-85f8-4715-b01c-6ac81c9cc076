#!/bin/bash

# Start <PERSON><PERSON><PERSON> in the background
ollama serve &

# Wait for <PERSON><PERSON><PERSON> to be ready
echo "Waiting for <PERSON>lla<PERSON> to start..."
while ! curl -s http://localhost:11434/api/tags > /dev/null; do
    sleep 1
done

echo "Ollama is ready. Pulling qwen3-coder:30b model..."
ollama pull qwen3-coder:30b

echo "Model pulled successfully. Ollama is ready to use!"

# Keep the container running
wait
